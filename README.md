# 🚀 语雀知识库批量下载工具 v2.0

> **智能拦截模式 + 高效批量下载 = 完美解决方案**

一个功能强大的语雀知识库批量下载工具，支持API拦截模式，可以快速获取并下载您的所有知识库文档。

## ✨ 核心特性

### 🎯 **智能拦截模式**
- **API拦截技术**: 通过浏览器自动拦截语雀API响应
- **零配置获取**: 无需手动输入知识库链接或ID
- **智能去重**: 自动识别并移除重复的知识库
- **一键启动**: 启动程序即可自动获取所有可访问的知识库

### 📚 **批量下载功能**  
- **多种下载模式**:
  - 🔄 **全部下载**: 一键下载选中知识库的所有文档
  - 🎯 **精选下载**: 为每个知识库单独选择需要的文档
- **文件格式支持**: Markdown、Word、PDF、图片等多种格式
- **智能文件管理**: 自动按知识库分类存储，避免文件冲突
- **断点续传**: 支持中断恢复，避免重复下载

### 🛡️ **稳定可靠**
- **异常处理**: 完善的错误处理和日志记录
- **资源管理**: 自动清理浏览器和网络资源
- **Session复用**: 保持登录状态，提高下载效率
- **进度显示**: 实时显示下载进度和状态

## 📦 安装说明

### 方式一：直接运行（推荐）

1. **下载程序包**
```bash
# 下载并解压程序包
wget https://github.com/yuque-downloader/releases/latest/download/yuque-downloader-v2.0.zip
unzip yuque-downloader-v2.0.zip
cd yuque-downloader-v2.0
```

2. **安装依赖**
```bash
# 使用pip安装依赖
pip install -r requirements.txt

# 或使用conda
conda install --file requirements.txt
```

3. **运行程序**
```bash
python main.py
```

### 方式二：PyPI安装

```bash
# 通过pip安装
pip install yuque-downloader

# 运行
yuque-downloader
# 或
yuque-dl
```

## 🚀 快速开始

### 第一次使用

1. **启动程序**
```bash
python main.py
```

2. **自动拦截知识库**
   - 程序会自动打开浏览器
   - 请在浏览器中登录您的语雀账号
   - 程序会自动拦截并获取您的知识库列表

3. **选择下载模式**
   - **选项1**: 选择知识库 + 下载所有文档 
   - **选项2**: 选择知识库 + 单独选择文档

4. **开始下载**
   - 选择您需要的知识库（支持多选）
   - 确认下载设置，开始批量下载

### 典型使用流程

```
🔄 启动拦截器 → 📚 获取知识库列表 → 🎯 选择下载目标 → 📥 批量下载文档
```

## 📋 系统要求

- **Python**: 3.8+
- **操作系统**: Windows 10+, macOS 10.14+, Ubuntu 18.04+
- **浏览器**: Chrome 88+ (程序会自动管理ChromeDriver)
- **内存**: 建议4GB以上
- **硬盘**: 足够存储下载文档的空间

## 🔧 配置说明

### 下载路径配置
程序默认将文档下载到 `./downloads/` 目录，您可以在 `config.py` 中修改：

```python
# 下载根目录
DOWNLOAD_ROOT = "./downloads"

# 输出控制
CONSOLE_OUTPUT_CONFIG = {
    "show_api_status": True,      # 显示API状态
    "show_progress": True,        # 显示进度信息
    "show_detailed_errors": False # 显示详细错误
}
```

### 浏览器配置
如需自定义Chrome浏览器路径或参数，可修改 `yuque_webdriver_manager.py`。

## 📁 输出结构

```
downloads/
├── 知识库名称1/
│   ├── 文档1.md
│   ├── 文档2.md
│   └── images/
│       ├── image1.png
│       └── image2.jpg
├── 知识库名称2/
│   ├── 文档A.md
│   ├── 文档B.md
│   └── attachments/
│       └── file.pdf
└── download_log_YYYYMMDD_HHMMSS.txt
```

## 🔍 日志和调试

### 日志文件
- **主日志**: `yuque_downloader.log` - 详细的运行日志
- **下载日志**: `download_log_YYYYMMDD_HHMMSS.txt` - 每次下载的摘要
- **拦截器日志**: 包含在主日志中，记录API拦截过程

### 调试模式
如遇问题，可以启用详细日志：

```python
# 在 config.py 中设置
CONSOLE_OUTPUT_CONFIG = {
    "show_detailed_errors": True  # 显示详细错误信息
}
```

## ❓ 常见问题

### Q: 浏览器无法启动？
A: 检查Chrome是否已安装，程序会自动下载对应版本的ChromeDriver。

### Q: 拦截不到知识库列表？
A: 确保已在浏览器中正确登录语雀账号，并访问过知识库页面。

### Q: 下载失败的文档？
A: 检查网络连接，某些私有文档可能需要特定权限。

### Q: Sheet文档无法下载？
A: Sheet类型文档需要手动导出，程序会提供详细的手动导出步骤。

### Q: 如何处理重复文件？
A: 程序自动跳过已存在的文件，可查看下载日志了解跳过详情。

## 🛠️ 开发说明

### 项目结构
```
yuque-downloader/
├── main.py                    # 程序入口
├── yuque_downloader.py        # 核心下载逻辑
├── yuque_books_interceptor.py # API拦截器
├── user_interface.py          # 用户交互界面
├── api_client.py              # API客户端
├── file_manager.py            # 文件管理
├── yuque_webdriver_manager.py # 浏览器管理
└── utils.py                   # 工具函数
```

### 核心依赖
```
selenium-wire>=5.1.0    # 网络拦截
selenium>=4.15.0        # 浏览器自动化  
requests>=2.31.0        # HTTP请求
tqdm>=4.66.0           # 进度条
beautifulsoup4>=4.12.0  # HTML解析
```

## 📄 许可证

本项目基于 MIT 许可证开源 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 🤝 贡献

欢迎提交Issue和Pull Request！

1. Fork 本仓库
2. 创建您的特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交您的更改 (`git commit -m 'feat: Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

## 📞 支持

- **GitHub Issues**: [提交问题](https://github.com/yuque-downloader/yuque-downloader/issues)
- **文档**: 查看本README和代码注释
- **讨论**: [GitHub Discussions](https://github.com/yuque-downloader/yuque-downloader/discussions)

---

⭐ **如果这个项目对您有帮助，请给我们一个Star！** ⭐ 