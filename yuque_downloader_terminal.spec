# -*- mode: python ; coding: utf-8 -*-

import os
import glob

block_cipher = None

# 获取所有Python文件
python_files = []
for py_file in glob.glob('*.py'):
    if not py_file.startswith('__'):  # 排除__pycache__等
        python_files.append((py_file, '.'))

# 收集所有Python文件和数据文件
a = Analysis(
    ['main.py'],
    pathex=['.'],
    binaries=[],
    datas=[
        ('requirements.txt', '.'),
        ('README.md', '.'),
        ('LICENSE', '.'),
        ('INSTALL.md', '.'),
        ('config.py', '.'),
        ('yuque_downloader.py', '.'),
        ('yuque_webdriver_manager.py', '.'),
        ('yuque_books_interceptor.py', '.'),
        ('user_interface.py', '.'),
        ('api_client.py', '.'),
        ('utils.py', '.'),
        ('path_manager.py', '.'),
        ('file_manager.py', '.'),
        ('exceptions.py', '.'),
        ('__init__.py', '.'),
        ('run.py', '.'),
        ('sync_auth.py', '.'),
        ('api_result.py', '.'),
        ('enhanced_main.py', '.'),
        # 添加ChromeDriver
        ('chromedriver', 'drivers/'),
    ] + python_files,
    hiddenimports=[
        'selenium',
        'seleniumwire',
        'requests',
        'tqdm',
        'beautifulsoup4',
        'urllib3',
        'certifi',
        'charset_normalizer',
        'idna',
        'websocket-client',
        'trio',
        'trio_websocket',
        'attrs',
        'sortedcontainers',
        'outcome',
        'sniffio',
        'h11',
        'wsproto',
        'typing_extensions',
        'webdriver_manager',
        'webdriver_manager.chrome',
        'json',
        'pathlib',
        'logging',
        'time',
        'os',
        'sys',
        'platform'
    ],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[
        'tkinter',
        'matplotlib',
        'numpy',
        'pandas',
        'PIL',
        'cv2',
        'scipy'
    ],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

# 生成单个可执行文件
exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='yuque-downloader',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=True,
    disable_windowed_traceback=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
) 