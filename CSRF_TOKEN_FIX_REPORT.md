# CSRF Token 403错误修复报告

## 问题概述

您遇到的错误：
```
ERROR: 请求失败 POST https://fjzx.yuque.com/api/docs/163237847/export (状态码: 403): 
both ctoken and referer check error: invalid csrf token, missing csrf referer or origin
```

这是一个典型的CSRF（跨站请求伪造）保护机制导致的403错误，表明：
1. **CSRF token 无效或缺失**
2. **Referer 头部缺失或不正确**  
3. **Origin 头部缺失或不正确**

## 修复方案

### 1. 增强API请求头管理 (`api_client.py`)

#### 新增功能：
- **动态API基础URL检测**：自动根据当前环境（企业版/公版）调整API请求地址
- **关键请求头确保机制**：每次API请求前自动设置必需的头部信息
- **实时CSRF token获取**：从当前页面动态提取最新的CSRF token

#### 关键代码改进：
```python
def _ensure_critical_headers(self, url: str):
    """确保关键请求头存在"""
    # 设置正确的Referer和Origin
    if 'fjzx.yuque.com' in current_url:
        referer = "https://fjzx.yuque.com/"
    else:
        referer = "https://www.yuque.com/"
    
    critical_headers = {
        'Referer': referer,
        'Origin': referer.rstrip('/'),
        'X-Requested-With': 'XMLHttpRequest',
        'Content-Type': 'application/json;charset=UTF-8',
        # ... 其他必要头部
    }
    
    # 特别处理导出API请求
    if '/export' in url:
        csrf_token = self._get_csrf_token_from_page()
        if csrf_token:
            critical_headers['X-CSRF-Token'] = csrf_token
```

### 2. 增强WebDriver会话管理 (`yuque_webdriver_manager.py`)

#### 新增功能：
- **智能CSRF token提取**：从多个来源提取CSRF token（meta标签、脚本、window对象）
- **增强请求头捕获**：更完整地捕获登录后的认证信息
- **实时反馈**：在捕获过程中提供详细的进度信息

#### 关键改进：
```python
def _extract_csrf_token_from_page(self) -> Optional[str]:
    """从当前页面提取CSRF token"""
    # 从多个位置查找CSRF token：
    # 1. meta[name="csrf-token"]
    # 2. meta[name="_token"]  
    # 3. 页面脚本中的token
    # 4. window对象中的token
```

## 预期效果

### 修复前：
```
❌ ERROR: 导出请求失败: 163237847 - both ctoken and referer check error
```

### 修复后：
```
✅ 成功捕获请求头，包含CSRF token
✅ 正确设置Referer和Origin头部
✅ 文档导出请求成功
```

## 使用建议

### 1. 重新测试文档下载
```bash
./yuque-downloader
# 或
python main.py
```

### 2. 观察日志输出
现在会看到更详细的请求头信息：
```
🔍 正在捕获请求头信息...
✅ 捕获到 15 个请求头
✅ 从页面获取到CSRF token: abcd123456...
```

### 3. 如果仍有问题
1. **检查登录状态**：确保在正确的语雀环境中登录
2. **查看日志**：检查 `./logs/yuque_downloader.log` 中的详细错误信息
3. **清理会话文件**：删除旧的 `yuque_*.json` 文件重新登录

## 技术细节

### CSRF Token 工作原理
1. **登录后捕获**：从登录成功页面提取有效的CSRF token
2. **实时更新**：每次API请求前重新获取最新token
3. **多源查找**：从meta标签、脚本、window对象等多个位置查找

### 请求头完整性
- ✅ **Referer**: 正确的语雀域名
- ✅ **Origin**: 与Referer匹配的域名
- ✅ **X-CSRF-Token**: 实时获取的有效token
- ✅ **Content-Type**: 正确的JSON类型
- ✅ **User-Agent**: 标准浏览器UA

### 环境适配
- 🏢 **企业版语雀** (fjzx.yuque.com)：自动适配企业版API和认证
- 🌐 **公版语雀** (www.yuque.com)：自动适配公版API和认证

## 后续维护

如果语雀更新了CSRF保护机制，可能需要：
1. 更新CSRF token提取规则
2. 调整请求头设置
3. 适配新的认证方式

---

**修复状态**: ✅ 已完成  
**测试状态**: 🧪 待验证  
**建议操作**: 重新运行下载程序测试效果