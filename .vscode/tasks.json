{"version": "2.0.0", "tasks": [{"label": "运行语雀下载器", "type": "shell", "command": "python", "args": ["main.py"], "group": {"kind": "build", "isDefault": true}, "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "new", "showReuseMessage": true, "clear": false}, "options": {"cwd": "${workspaceFolder}"}, "problemMatcher": []}, {"label": "安装依赖", "type": "shell", "command": "pip", "args": ["install", "-r", "requirements.txt"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "new"}, "options": {"cwd": "${workspaceFolder}"}, "problemMatcher": []}, {"label": "检查代码格式", "type": "shell", "command": "flake8", "args": ["--max-line-length=88", "--extend-ignore=E203,W503", "."], "group": "test", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "new"}, "options": {"cwd": "${workspaceFolder}"}, "problemMatcher": []}, {"label": "格式化代码", "type": "shell", "command": "black", "args": ["--line-length=88", "."], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "new"}, "options": {"cwd": "${workspaceFolder}"}, "problemMatcher": []}, {"label": "清理缓存文件", "type": "shell", "command": "find", "args": [".", "-type", "f", "-name", "*.pyc", "-delete"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "new"}, "options": {"cwd": "${workspaceFolder}"}, "problemMatcher": []}, {"label": "查看日志", "type": "shell", "command": "tail", "args": ["-f", "yuque_downloader.log"], "group": "test", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "new"}, "options": {"cwd": "${workspaceFolder}"}, "problemMatcher": [], "isBackground": true}]}