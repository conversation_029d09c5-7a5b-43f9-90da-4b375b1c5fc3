{"version": "0.2.0", "configurations": [{"name": "运行语雀下载器", "type": "python", "request": "launch", "program": "${workspaceFolder}/main.py", "console": "integratedTerminal", "cwd": "${workspaceFolder}", "env": {"PYTHONPATH": "${workspaceFolder}"}, "args": [], "justMyCode": true, "stopOnEntry": false, "showReturnValue": true}, {"name": "调试模式运行语雀下载器", "type": "python", "request": "launch", "program": "${workspaceFolder}/main.py", "console": "integratedTerminal", "cwd": "${workspaceFolder}", "env": {"PYTHONPATH": "${workspaceFolder}", "PYTHONUNBUFFERED": "1"}, "args": [], "justMyCode": false, "stopOnEntry": false, "showReturnValue": true, "logToFile": true, "debugOptions": ["RedirectOutput"]}, {"name": "运行测试", "type": "python", "request": "launch", "program": "${workspaceFolder}/test_core_functionality.py", "console": "integratedTerminal", "cwd": "${workspaceFolder}", "env": {"PYTHONPATH": "${workspaceFolder}"}, "args": [], "justMyCode": true, "stopOnEntry": false}, {"name": "运行指定Python文件", "type": "python", "request": "launch", "program": "${file}", "console": "integratedTerminal", "cwd": "${workspaceFolder}", "env": {"PYTHONPATH": "${workspaceFolder}"}, "args": [], "justMyCode": true, "stopOnEntry": false}]}