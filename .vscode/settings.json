{"python.defaultInterpreterPath": "./venv/bin/python", "python.terminal.activateEnvironment": true, "python.linting.enabled": true, "python.linting.pylintEnabled": false, "python.linting.flake8Enabled": true, "python.formatting.provider": "black", "python.formatting.blackArgs": ["--line-length=88"], "python.sortImports.args": ["--profile", "black"], "editor.formatOnSave": true, "editor.codeActionsOnSave": {"source.organizeImports": "explicit"}, "files.exclude": {"**/__pycache__": true, "**/*.pyc": true, "**/.pytest_cache": true, "**/node_modules": true, "**/.git": true, "**/.DS_Store": true, "**/Thumbs.db": true}, "python.analysis.extraPaths": ["${workspaceFolder}"], "python.analysis.autoImportCompletions": true, "python.analysis.typeCheckingMode": "basic", "terminal.integrated.env.osx": {"PYTHONPATH": "${workspaceFolder}"}, "terminal.integrated.env.linux": {"PYTHONPATH": "${workspaceFolder}"}, "terminal.integrated.env.windows": {"PYTHONPATH": "${workspaceFolder}"}, "CodeGPT.apiKey": "CodeGPT Plus Beta"}