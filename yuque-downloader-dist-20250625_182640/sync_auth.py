#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
认证同步工具 - 确保主程序使用有效的认证信息
"""

import json
import os
from yuque_webdriver_manager import WebDriverManager

def sync_authentication():
    """同步认证信息到主程序"""
    print("🔄 同步认证信息...")
    
    # 检查保存的认证文件
    auth_files = ['yuque_headers.json', 'yuque_cookies.json']
    
    for file in auth_files:
        if os.path.exists(file):
            print(f"✅ 发现有效认证文件: {file}")
        else:
            print(f"❌ 缺少认证文件: {file}")
            return False
    
    print("✅ 认证信息同步完成")
    return True

if __name__ == "__main__":
    sync_authentication()
