from setuptools import setup, find_packages
import os

# 读取README
def read_readme():
    if os.path.exists("README.md"):
        with open("README.md", "r", encoding="utf-8") as f:
            return f.read()
    return "语雀知识库批量下载工具"

# 读取requirements
def read_requirements():
    with open("requirements.txt", "r", encoding="utf-8") as f:
        return [line.strip() for line in f if line.strip() and not line.startswith("#")]

setup(
    name="yuque-downloader",
    version="2.0.0",
    description="语雀知识库批量下载工具 - 支持API拦截模式",
    long_description=read_readme(),
    long_description_content_type="text/markdown",
    author="YuqueDownloader Team",
    author_email="<EMAIL>",
    url="https://github.com/yuque-downloader/yuque-downloader",
    
    # 包配置
    packages=find_packages(),
    include_package_data=True,
    
    # 依赖
    install_requires=read_requirements(),
    
    # Python版本要求
    python_requires=">=3.8",
    
    # 分类
    classifiers=[
        "Development Status :: 4 - Beta",
        "Intended Audience :: Developers",
        "Intended Audience :: End Users/Desktop",
        "License :: OSI Approved :: MIT License",
        "Operating System :: OS Independent",
        "Programming Language :: Python :: 3",
        "Programming Language :: Python :: 3.8",
        "Programming Language :: Python :: 3.9",
        "Programming Language :: Python :: 3.10",
        "Programming Language :: Python :: 3.11",
        "Topic :: Internet :: WWW/HTTP",
        "Topic :: Software Development :: Libraries :: Python Modules",
        "Topic :: Text Processing :: Markup :: HTML",
        "Topic :: Utilities"
    ],
    
    # 关键词
    keywords="yuque, downloader, api, selenium, automation, knowledge-base",
    
    # 命令行入口
    entry_points={
        "console_scripts": [
            "yuque-downloader=main:main",
            "yuque-dl=main:main",
        ],
    },
    
    # 包数据
    package_data={
        "": [
            "*.txt",
            "*.md",
            "*.json",
            "*.cfg",
            "*.ini"
        ],
    },
    
    # 额外文件
    data_files=[
        (".", ["requirements.txt"]),
    ],
) 