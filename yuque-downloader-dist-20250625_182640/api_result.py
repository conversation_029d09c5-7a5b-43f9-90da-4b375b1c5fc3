"""
API结果封装模块
定义APIResult类来封装API调用的结果，实现更优雅的错误处理
"""

from typing import Optional, Any
from config import YUQUE_API_BASE


class APIResult:
    """API调用结果封装类"""
    
    def __init__(self, success: bool, data: Any = None, error_msg: str = "", 
                 status_code: Optional[int] = None, url: str = "", method: str = ""):
        """
        初始化API结果
        
        Args:
            success: 是否成功
            data: 返回的数据
            error_msg: 错误信息
            status_code: HTTP状态码
            url: 请求URL
            method: HTTP方法
        """
        self.success = success
        self.data = data
        self.error_msg = error_msg
        self.status_code = status_code
        self.url = url
        self.method = method
    
    @classmethod
    def success_result(cls, data: Any, status_code: int = 200, url: str = "", method: str = ""):
        """创建成功结果"""
        return cls(True, data, "", status_code, url, method)
    
    @classmethod
    def error_result(cls, error_msg: str, status_code: Optional[int] = None, 
                    url: str = "", method: str = ""):
        """创建错误结果"""
        return cls(False, None, error_msg, status_code, url, method)
    
    def get_endpoint(self) -> str:
        """从完整URL提取API端点"""
        if self.url:
            # 移除基础URL，只保留端点部分
            endpoint = self.url.replace(YUQUE_API_BASE, "")
            # 移除查询参数，只保留路径
            if "?" in endpoint:
                endpoint = endpoint.split("?")[0]
            return endpoint
        return "unknown"
    
    def __str__(self) -> str:
        """字符串表示"""
        status = "SUCCESS" if self.success else "ERROR"
        return f"APIResult({status}, {self.method} {self.get_endpoint()}, {self.status_code})"
    
    def __repr__(self) -> str:
        """详细字符串表示"""
        return (f"APIResult(success={self.success}, method='{self.method}', "
                f"endpoint='{self.get_endpoint()}', status_code={self.status_code}, "
                f"error_msg='{self.error_msg}')")
