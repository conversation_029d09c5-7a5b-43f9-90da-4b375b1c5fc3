# 🚀 语雀下载工具 - macOS版本

> **一键式macOS应用 - 无需安装Python环境**

专为macOS用户打造的语雀知识库批量下载工具，打包成独立的`.app`应用，无需安装Python或任何依赖包。

## 📦 下载和安装

### 🎯 直接下载（推荐）

**下载文件**: `YuqueDownloader-v2.0.0-macOS.dmg` (27MB)

### 📋 安装步骤

1. **下载DMG文件**
   - 双击 `YuqueDownloader-v2.0.0-macOS.dmg`
   - 系统会自动挂载磁盘映像

2. **安装应用**
   ```
   将 YuqueDownloader.app 拖拽到 Applications 文件夹
   ```

3. **首次启动**
   - 在 Applications 中找到 YuqueDownloader
   - 右键点击 → "打开"
   - 系统会提示安全警告，点击"打开"确认

## 🚀 使用指南

### ⚡ 一键启动
```bash
# 方式1: 在Applications中双击启动
双击 YuqueDownloader.app

# 方式2: 在终端中启动
open /Applications/YuqueDownloader.app
```

### 📋 使用流程

1. **启动应用** - 双击YuqueDownloader.app
2. **自动拦截** - 程序会自动打开Chrome浏览器
3. **登录语雀** - 在浏览器中登录您的语雀账号
4. **选择知识库** - 从拦截到的列表中选择要下载的知识库
5. **开始下载** - 选择下载模式后自动开始下载

### 📁 文件位置

下载的文档会保存在应用同级目录的`downloads`文件夹中：
```
/Applications/YuqueDownloader.app/
├── YuqueDownloader.app           # 主应用
└── downloads/                    # 下载目录（自动创建）
    ├── 知识库1/
    ├── 知识库2/
    └── ...
```

## 🔧 系统要求

### ✅ 支持的macOS版本
- **macOS 10.14** (Mojave) 或更高版本
- **Intel** 和 **Apple Silicon** (M1/M2/M3) 芯片均支持
- **Chrome浏览器** 88+ （必需，用于API拦截）

### 💾 硬盘空间
- **应用大小**: 约27MB
- **下载空间**: 根据您的知识库文档数量而定

## 🛡️ 安全说明

### 🔒 Gatekeeper警告
首次启动时，macOS Gatekeeper可能会显示安全警告：

```
"YuqueDownloader.app" 无法打开，因为它来自身份不明的开发者
```

**解决方法**：
1. 右键点击应用
2. 选择"打开"
3. 在弹出的对话框中点击"打开"
4. 或在系统偏好设置 → 安全与隐私中允许

### 🔐 权限说明
应用需要以下权限：
- **网络访问**: 用于访问语雀API和下载文档
- **文件系统访问**: 用于保存下载的文档
- **Chrome浏览器控制**: 用于API拦截功能

## 🎯 功能特性

### 🚀 **即开即用**
- ✅ 无需安装Python环境
- ✅ 无需配置依赖包
- ✅ 一键启动，自动工作

### 📚 **智能下载**
- ✅ API拦截技术，自动获取知识库列表
- ✅ 支持批量下载和精选下载两种模式
- ✅ 自动去重，避免重复下载
- ✅ 智能文件管理，按知识库分类存储

### 🛡️ **稳定可靠**
- ✅ 完善的错误处理和恢复机制
- ✅ 详细的日志记录，便于问题排查
- ✅ 资源自动清理，不占用系统资源

## 🐛 故障排除

### 常见问题和解决方案

**Q1: 应用无法启动，显示"已损坏"**
```bash
# 解决方法: 移除扩展属性
xattr -cr /Applications/YuqueDownloader.app
```

**Q2: Chrome浏览器无法启动**
```
问题: ChromeDriver版本不匹配
解决: 确保Chrome浏览器已更新到最新版本
```

**Q3: 权限被拒绝**
```
问题: macOS阻止了应用的网络或文件访问
解决: 系统偏好设置 → 安全与隐私 → 隐私 → 授予相应权限
```

**Q4: 下载失败或中断**
```
问题: 网络连接问题或语雀权限问题
解决: 检查网络连接，确保在浏览器中能正常访问语雀
```

### 🔍 日志文件
如遇问题，可查看日志文件：
```bash
# 应用日志位置
~/Library/Logs/YuqueDownloader/
```

## 🆚 版本对比

| 特性 | Python源码版 | macOS应用版 |
|------|-------------|------------|
| 安装复杂度 | 需要Python环境 | 直接安装.app |
| 启动方式 | 命令行启动 | 双击启动 |
| 依赖管理 | 手动安装依赖 | 内置所有依赖 |
| 更新方式 | Git拉取更新 | 重新下载DMG |
| 文件大小 | ~1MB源码 | ~27MB独立应用 |
| 适用用户 | 开发者 | 普通用户 |

## 📞 技术支持

### 🔧 **自助排查**
1. 检查Chrome浏览器版本
2. 确认网络连接正常
3. 查看应用日志文件
4. 重启应用重试

### 📧 **联系支持**
- GitHub Issues: [提交问题](https://github.com/yuque-downloader/issues)
- 邮件支持: <EMAIL>

## 📄 许可证

本应用基于 MIT 许可证开源。

---

## 🎉 快速开始

1. **下载**: `YuqueDownloader-v2.0.0-macOS.dmg`
2. **安装**: 拖拽到Applications文件夹
3. **启动**: 双击应用图标
4. **登录**: 在自动打开的浏览器中登录语雀
5. **下载**: 选择知识库，开始批量下载

**就是这么简单！** 🚀

---

⭐ **如果这个应用对您有帮助，请给我们一个Star！** ⭐ 