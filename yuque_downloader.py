"""
语雀文档下载器主类
整合所有模块，实现完整的下载流程
"""

import logging
import signal
import sys
import time
import json
import os
from typing import List, Dict, Optional
from pathlib import Path

from yuque_webdriver_manager import WebDriverManager
from api_client import YuqueAP<PERSON>lient
from user_interface import UserInterface
from file_manager import FileManager
from utils import setup_logging, validate_book_data, validate_doc_data, normalize_books_data
from api_result import APIResult
from exceptions import (
    YuqueException, WebDriverException, LoginException,
    APIException, DownloadException, UserInterruptException
)

logger = logging.getLogger(__name__)


class YuqueDownloader:
    """语雀文档下载器主类"""
    
    def __init__(self):
        self.webdriver_manager = None
        self.api_client = None
        self.ui = UserInterface()
        self.file_manager = FileManager()
        self.is_running = True
        
        # 设置信号处理
        signal.signal(signal.SIGINT, self._signal_handler)
        signal.signal(signal.SIGTERM, self._signal_handler)
    
    def _signal_handler(self, signum, frame):
        """处理中断信号"""
        print("\n\n⚠️  检测到中断信号，正在安全退出...")
        self.is_running = False
        self.cleanup()
        sys.exit(0)
    
    def initialize(self) -> bool:
        """初始化下载器"""
        try:
            logger.info("初始化语雀文档下载器...")

            # 子步骤1.1: 初始化Chrome浏览器
            print("   🔧 步骤 1.1: 初始化Chrome浏览器...")
            start_time = time.time()
            self.webdriver_manager = WebDriverManager()
            self.webdriver_manager.initialize_driver()
            init_time = time.time() - start_time
            print(f"   ✅ 步骤 1.1 完成: Chrome浏览器初始化成功 (耗时: {init_time:.2f}秒)")

            # 子步骤1.2: 初始化API客户端
            print("   🔧 步骤 1.2: 初始化API客户端...")
            self.api_client = YuqueAPIClient(self.webdriver_manager)
            print("   ✅ 步骤 1.2 完成: API客户端初始化成功")

            # 子步骤1.3: 初始化文件管理器和用户界面
            print("   🔧 步骤 1.3: 初始化文件管理器和用户界面...")
            self.file_manager = FileManager()
            self.ui = UserInterface()
            print("   ✅ 步骤 1.3 完成: 文件管理器和用户界面初始化成功")

            logger.info("下载器初始化完成")
            return True

        except Exception as e:
            logger.error(f"初始化失败: {e}")
            print(f"   ❌ 初始化失败: {e}")
            raise WebDriverException(f"初始化失败: {e}")
    
    def login(self) -> bool:
        """处理用户登录"""
        try:
            logger.info("开始登录流程...")

            # 子步骤2.1: 检查当前页面地址
            print("   🔧 步骤 2.1: 检查当前页面地址...")
            current_url = self.webdriver_manager.driver.current_url
            logger.info(f"当前页面URL: {current_url}")

            # 如果当前地址在知识库页面，认为已登录成功
            if 'dashboard/books' in current_url:
                print("   ✅ 步骤 2.1 完成: 当前已在知识库页面，登录状态有效")
                logger.info("检测到已在知识库页面，登录状态有效")

                # 子步骤2.2: 捕获认证信息
                print("   🔧 步骤 2.2: 捕获认证信息...")
                self.webdriver_manager.capture_request_headers()
                print("   ✅ 步骤 2.2 完成: 认证信息捕获成功")

                logger.info("登录检查完成")
                return True

            # 如果不在知识库页面，说明需要登录
            print(f"   ⚠️ 步骤 2.1: 当前页面不在知识库页面 ({current_url})，需要登录")

            # 子步骤2.2: 等待用户登录
            print("   🔧 步骤 2.2: 等待用户登录...")
            print("🔐 需要登录语雀账号，请在浏览器中完成登录")
            print("💡 登录后请访问 https://www.yuque.com/dashboard/books 页面")

            # 等待用户导航到知识库页面
            max_wait_time = 300  # 5分钟
            start_time = time.time()

            while time.time() - start_time < max_wait_time:
                current_url = self.webdriver_manager.driver.current_url

                # 检查是否已经在知识库页面
                if 'dashboard/books' in current_url:
                    print("   ✅ 步骤 2.2 完成: 用户登录成功，已导航到知识库页面")

                    # 子步骤2.3: 捕获认证信息
                    print("   🔧 步骤 2.3: 捕获认证信息...")
                    self.webdriver_manager.capture_request_headers()
                    print("   ✅ 步骤 2.3 完成: 认证信息捕获成功")

                    logger.info("登录成功")
                    return True

                # 每10秒提示一次
                elapsed = time.time() - start_time
                if int(elapsed) % 10 == 0 and elapsed > 0:
                    print(f"   ⏳ 等待登录中... ({int(elapsed)}/{max_wait_time}秒)")

                time.sleep(2)  # 每2秒检查一次

            print("   ❌ 步骤 2.2 失败: 登录等待超时")
            raise LoginException("登录超时")

        except Exception as e:
            logger.error(f"登录失败: {e}")
            print(f"   ❌ 登录失败: {e}")
            raise LoginException(f"登录失败: {e}")
    
    def get_books(self) -> List[Dict]:
        """获取知识库列表 - 智能API选择"""
        try:
            logger.info("获取知识库列表...")

            # 子步骤3.1: 根据环境智能选择API
            print("   🔧 步骤 3.1: 发起知识库API请求...")
            start_time = time.time()

            # 检测当前环境并选择合适的API
            current_url = self.webdriver_manager.driver.current_url
            is_enterprise = 'fjzx.yuque.com' in current_url

            if is_enterprise:
                print("   🏢 检测到企业版语雀环境，使用 user_books API...")
                # 企业版优先使用user_books API，增加limit到100
                result = self.api_client.get_user_books(offset=0, limit=100, query="", user_type="Group")

                # 如果user_books失败，尝试book_stacks作为备用
                if not result.success:
                    print("   🔄 user_books API失败，尝试 book_stacks API...")
                    result = self.api_client.get_book_stacks()
            else:
                print("   🌐 检测到公版语雀环境，使用 book_stacks API...")
                # 公版优先使用book_stacks API
                result = self.api_client.get_book_stacks()

                # 如果book_stacks失败，尝试user_books作为备用
                if not result.success:
                    print("   🔄 book_stacks API失败，尝试 user_books API...")
                    result = self.api_client.get_user_books(offset=0, limit=100, query="", user_type="Group")

            api_time = time.time() - start_time
            print(f"   ✅ 步骤 3.1 完成: API请求成功 (耗时: {api_time:.2f}秒)")

            # 显示API状态
            self.ui.show_api_status(result)

            if not result.success:
                print(f"   ❌ 步骤 3.1 失败: {result.error_msg}")
                logger.error(f"获取知识库失败: {result.error_msg}")
                return []

            # 子步骤3.2: 处理和验证数据
            print("   🔧 步骤 3.2: 处理和验证知识库数据...")
            raw_data = result.data
            logger.debug(f"原始数据类型: {type(raw_data)}")
            logger.debug(f"原始数据内容: {raw_data}")

            # 标准化数据格式
            books = normalize_books_data(raw_data)
            logger.debug(f"标准化后books类型: {type(books)}, 长度: {len(books)}")

            # 验证数据
            valid_books = [book for book in books if validate_book_data(book)]

            if not valid_books:
                print("   ❌ 步骤 3.2 失败: 没有有效的知识库数据")
                logger.warning("没有有效的知识库数据")
                return []

            print(f"   ✅ 步骤 3.2 完成: 数据验证成功，获得 {len(valid_books)} 个有效知识库")
            logger.info(f"获取到 {len(valid_books)} 个有效知识库")
            return valid_books

        except Exception as e:
            print(f"   ❌ 获取知识库异常: {e}")
            logger.error(f"获取知识库异常: {e}")
            return []
    
    def get_docs(self, book_id: str) -> List[Dict]:
        """获取文档列表"""
        try:
            result = self.api_client.get_docs(book_id)

            # 显示API状态
            self.ui.show_api_status(result)

            if not result.success:
                logger.warning(f"知识库 {book_id} 获取文档失败: {result.error_msg}")
                return []

            docs = result.data or []
            if not docs:
                logger.warning(f"知识库 {book_id} 没有文档")
                return []

            # 验证数据
            valid_docs = [doc for doc in docs if validate_doc_data(doc)]

            logger.info(f"知识库 {book_id} 获取到 {len(valid_docs)} 个有效文档")
            return valid_docs

        except Exception as e:
            logger.error(f"获取文档列表异常 {book_id}: {e}")
            return []
    
    def download_documents(self, books: List[Dict], download_mode: str = "all") -> Dict:
        """下载文档"""
        total_books = len(books)
        total_docs = 0
        success_count = 0
        failed_count = 0

        try:
            for book_index, book in enumerate(books, 1):
                if not self.is_running:
                    break

                book_name = book.get('name', '未知知识库')
                book_id = book.get('id')

                print(f"\n📚 处理知识库 ({book_index}/{total_books}): {book_name}")

                # 获取文档列表
                docs = self.get_docs(book_id)
                if not docs:
                    print(f"⚠️  知识库 '{book_name}' 没有可下载的文档")
                    continue

                # 根据下载模式选择文档
                if download_mode == "all":
                    # 下载所有文档（过滤掉Board类型）
                    selected_docs = [doc for doc in docs if doc.get('type') != 'Board']
                    print(f"📄 将下载知识库中的所有文档 ({len(selected_docs)} 个)")
                elif download_mode == "select":
                    # 让用户选择文档
                    valid_docs = self.ui.display_docs(docs, book_name)
                    selected_docs = self.ui.select_docs(valid_docs, book_name)

                    if not selected_docs:
                        print(f"⏭️  跳过知识库 '{book_name}'")
                        continue
                else:
                    # 默认下载所有文档
                    selected_docs = [doc for doc in docs if doc.get('type') != 'Board']

                # 创建知识库目录
                book_dir = self.file_manager.create_book_directory(book_name)

                # 下载选中的文档
                total_docs += len(selected_docs)
                book_success, book_failed = self._download_book_docs(
                    selected_docs, book_dir, book_name
                )

                success_count += book_success
                failed_count += book_failed
            
            return {
                'total_books': total_books,
                'total_docs': total_docs,
                'success_count': success_count,
                'failed_count': failed_count
            }
            
        except Exception as e:
            logger.error(f"下载过程中发生错误: {e}")
            raise DownloadException(f"下载失败: {e}")
    
    def _download_book_docs(self, docs: List[Dict], book_dir: Path, book_name: str) -> tuple:
        """下载单个知识库的文档"""
        success_count = 0
        failed_count = 0
        total_docs = len(docs)
        
        for doc_index, doc in enumerate(docs, 1):
            if not self.is_running:
                break
            
            doc_title = doc.get('title', '未知文档')
            doc_id = doc.get('id')
            doc_type = doc.get('type', 'unknown')
            
            # 跳过Board类型文档
            if doc_type == 'Board':
                print(f"⚠️  跳过Board类型文档: {doc_title}")
                continue
            
            self.ui.show_simple_progress(doc_index, total_docs, doc_title)
            
            try:
                # 检查是否是Excel文档，如果是则直接跳过
                if doc_type.lower() == 'sheet':
                    print(f"📊 {doc_title} - Excel文档已跳过，请手动下载")
                    print(f"   💡 提示: 请在语雀网页中手动下载此Excel文档")

                    # 记录为跳过的文件
                    self.file_manager.downloaded_files[f"skipped_excel_{doc_id}"] = {
                        'url': 'manual_download_required',
                        'size': 0,
                        'path': f"Excel文档: {doc_title}",
                        'status': 'skipped_excel',
                        'title': doc_title,
                        'doc_id': doc_id
                    }

                    success_count += 1  # 计为成功处理
                    continue

                # 导出非Excel文档
                export_result = self.api_client.export_doc(doc_id, doc_type)

                # 显示API状态
                self.ui.show_api_status(export_result)

                if not export_result.success:
                    logger.error(f"文档 {doc_title} 导出失败: {export_result.error_msg}")
                    failed_count += 1
                    print(f"❌ {doc_title} - 导出失败: {export_result.error_msg}")
                    continue

                download_url = export_result.data

                # 处理非Excel文档
                filename = self.file_manager.generate_filename(doc_title)
                file_path = book_dir / filename

                # 下载文件
                download_success, download_status = self.file_manager.download_and_save(download_url, file_path, self.api_client)

                if download_success:
                    if download_status == 'skipped':
                        # 跳过的文件不计入成功下载
                        print(f"⏭️ {doc_title}")
                    else:
                        # 真正下载成功的文件
                        success_count += 1
                        print(f"✅ {doc_title}")
                else:
                    failed_count += 1
                    print(f"❌ {doc_title}")
                
            except Exception as e:
                logger.error(f"下载文档失败 {doc_title}: {e}")
                failed_count += 1
                print(f"❌ {doc_title} - {str(e)}")
        
        return success_count, failed_count
    
    def run(self):
        """主运行流程 - 使用拦截器模式"""
        try:
            logger.info("启动语雀文档下载器")
            
            print("🚀 语雀文档下载器启动")
            print("=" * 60)

            # 第1步: 使用拦截器获取书籍列表
            print("\n📋 步骤 1/3: 获取书籍列表")
            print("🔄 正在使用拦截器获取书籍列表...")
            
            books = self.get_books_via_interceptor()
            
            if not books:
                print("❌ 拦截器获取书籍列表失败，程序退出")
                return
            
            print(f"✅ 步骤 1/3 完成: 成功获取 {len(books)} 个书籍")

            # 第2步: 用户选择和配置
            print(f"\n📋 步骤 2/3: 用户选择配置")
            self._handle_download_flow(books)

        except UserInterruptException:
            print("\n👋 用户中断下载")
        except Exception as e:
            logger.error(f"运行过程中发生错误: {e}")
            print(f"❌ 运行失败: {e}")
        finally:
            self.cleanup()
    

    

    
    def _handle_download_flow(self, books: List[Dict]):
        """处理下载流程"""
        try:
            def refresh_books():
                """刷新知识库列表的回调函数"""
                if self.api_client:
                    return self.get_books()
                else:
                    print("⚠️ 传统模式未初始化，无法刷新")
                    return books

            # 用户交互和文档选择
            print("🔄 准备用户选择界面...")

            selected_books, download_mode = self.ui.select_books_and_mode(books, refresh_books)

            if not selected_books:
                print("👋 未选择任何知识库")
                return

            # 确认下载 - 只在下载所有文档模式时确认
            if download_mode == "all":
                if not self.ui.confirm_download_with_summary(selected_books, download_mode):
                    print("👋 用户取消下载")
                    return
            else:
                # 单独选择文档模式，不需要预先确认
                print(f"📋 已选择 {len(selected_books)} 个知识库，模式: 单独选择文档")

            print(f"✅ 步骤 2/3 完成: 已选择 {len(selected_books)} 个知识库，下载模式: {download_mode}")

            # 检查API客户端是否已初始化（通过拦截器已经初始化）
            if not self.api_client:
                print("🔧 初始化API客户端...")
                from api_client import YuqueAPIClient
                # 创建一个简单的WebDriver管理器实例用于API客户端
                if not self.webdriver_manager:
                    from yuque_webdriver_manager import WebDriverManager
                    self.webdriver_manager = WebDriverManager()
                    self.webdriver_manager.initialize_driver()
                self.api_client = YuqueAPIClient(self.webdriver_manager)
                print("✅ API客户端初始化完成")
            else:
                print("✅ API客户端已初始化，跳过重复初始化")

            # 第3步: 开始下载
            print(f"\n📋 步骤 3/3: 开始下载文档")
            print("🔄 正在下载文档...")

            result = self.download_documents(selected_books, download_mode)

            print("✅ 步骤 3/3 完成: 文档下载完成")

            # 显示结果摘要
            print("\n" + "="*60)
            print("🎉 下载任务完成")
            print("="*60)

            # 获取文件管理器摘要
            summary = self.file_manager.get_download_summary() if hasattr(self.file_manager, 'get_download_summary') else {}

            self.ui.print_summary(
                total_books=result['total_books'],
                total_docs=result['total_docs'],
                success_count=result['success_count'],
                failed_count=result['failed_count'],
                skipped_count=summary.get('skipped_files', 0)
            )

            # 导出下载日志
            if hasattr(self.file_manager, 'export_download_log'):
                self.file_manager.export_download_log()

        except Exception as e:
            logger.error(f"下载流程失败: {e}")
            print(f"❌ 下载流程失败: {e}")
    
    def cleanup(self):
        """清理资源"""
        try:
            if self.file_manager:
                self.file_manager.cleanup_failed_downloads()
            
            if self.api_client:
                self.api_client.close()
            
            if self.webdriver_manager:
                self.webdriver_manager.close()
            
            logger.info("资源清理完成")
            
        except Exception as e:
            logger.error(f"清理资源时发生错误: {e}")
    
    def _deduplicate_books_by_id(self, books: List[Dict]) -> List[Dict]:
        """根据ID去重书籍列表"""
        try:
            seen_ids = set()
            unique_books = []
            
            for book in books:
                book_id = book.get('id')
                if book_id is not None and book_id not in seen_ids:
                    seen_ids.add(book_id)
                    unique_books.append(book)
                elif book_id in seen_ids:
                    logger.debug(f"跳过重复书籍: ID={book_id}, 名称={book.get('name', '未知')}")
            
            return unique_books
            
        except Exception as e:
            logger.error(f"去重操作失败: {e}")
            return books  # 如果去重失败，返回原列表
    
    def _initialize_api_client_with_interceptor(self, interceptor):
        """使用拦截器的WebDriver初始化API客户端"""
        try:
            if not self.api_client and interceptor.webdriver_manager:
                from api_client import YuqueAPIClient
                # 复用拦截器的WebDriver管理器
                self.webdriver_manager = interceptor.webdriver_manager
                self.api_client = YuqueAPIClient(self.webdriver_manager)
                logger.info("API客户端初始化完成，复用拦截器WebDriver")
                print("✅ API客户端初始化完成 (复用拦截器浏览器)")
                return True
            else:
                logger.warning("拦截器WebDriver不可用，API客户端初始化跳过")
                return False
        except Exception as e:
            logger.error(f"API客户端初始化失败: {e}")
            print(f"⚠️ API客户端初始化失败: {e}")
            return False

    def get_books_via_interceptor(self) -> List[Dict]:
        """使用拦截器获取书籍列表"""
        try:
            self.ui.show_interceptor_status("initializing", "正在初始化书籍拦截器...")
            
            # 导入拦截器
            from yuque_books_interceptor import YuqueBooksInterceptor
            
            # 创建拦截器实例
            interceptor = YuqueBooksInterceptor()
            
            # 初始化拦截器
            if not interceptor.initialize():
                self.ui.show_interceptor_status("error", "拦截器初始化失败")
                return []
            
            # 同时初始化API客户端，复用拦截器的WebDriver
            self.ui.show_interceptor_status("progress", "正在初始化API客户端...")
            self._initialize_api_client_with_interceptor(interceptor)
            
            # 执行拦截
            self.ui.show_interceptor_status("intercepting", "正在拦截书籍列表API...")
            books = interceptor.intercept_books_api()
            
            if books:
                # 根据ID去重（额外保障）
                unique_books = self._deduplicate_books_by_id(books)
                if len(unique_books) != len(books):
                    removed_count = len(books) - len(unique_books)
                    print(f"🔄 主程序额外去重: 移除了 {removed_count} 个重复书籍")
                    books = unique_books
                
                # 成功完成，不向用户显示具体数量
                logger.info(f"成功拦截到 {len(books)} 个书籍")
                # 保存书籍列表到文件
                interceptor.save_books_to_file("intercepted_books.json")
                return books
            else:
                self.ui.show_interceptor_status("error", "拦截失败，未获取到书籍数据")
                return []
                
        except Exception as e:
            self.ui.show_interceptor_status("error", f"拦截器运行失败: {e}")
            logger.error(f"拦截器运行失败: {e}")
            return []
        finally:
            # 注意：不在这里关闭拦截器，因为API客户端需要复用其WebDriver
            # 拦截器的WebDriver会在主程序结束时通过cleanup()方法统一关闭
            logger.info("拦截器任务完成，WebDriver已移交给API客户端继续使用")
    
    def load_saved_books(self) -> List[Dict]:
        """从已保存的文件加载书籍列表"""
        try:
            saved_files = [
                "intercepted_books.json",
                "user_books_response.json", 
                "book_stacks_response.json"
            ]
            
            for file_path in saved_files:
                if os.path.exists(file_path):
                    print(f"📂 找到保存的书籍文件: {file_path}")
                    
                    with open(file_path, 'r', encoding='utf-8') as f:
                        data = json.load(f)
                    
                    if isinstance(data, list):
                        books = data
                    else:
                        # 如果是API响应格式，提取books字段
                        books = data.get('data', {}).get('books', [])
                    
                    if books:
                        print(f"✅ 成功加载 {len(books)} 个书籍")
                        return books
            
            print("❌ 未找到任何保存的书籍文件")
            return []
            
        except Exception as e:
            print(f"❌ 加载保存的书籍文件失败: {e}")
            logger.error(f"加载保存的书籍文件失败: {e}")
            return []
