#!/usr/bin/env python3
"""
语雀下载器简化启动脚本
用于开发和测试，自动处理路径配置
"""

import sys
import os
from pathlib import Path

# 添加当前目录到Python路径
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

def main():
    """简化的主函数"""
    print("🚀 语雀文档下载器 (开发版)")
    print("=" * 50)
    
    try:
        # 设置环境
        from path_manager import setup_environment, path_manager
        print("🔧 初始化环境...")
        dynamic_config = setup_environment()
        path_manager.print_environment_info()
        
        # 检查基本依赖
        print("\n🔍 检查依赖...")
        try:
            import selenium
            import seleniumwire
            import requests
            print("✅ 核心依赖可用")
        except ImportError as e:
            print(f"❌ 缺少依赖: {e}")
            print("请运行: pip install selenium selenium-wire requests")
            return
        
        # 启动主程序
        print("\n🎯 启动下载器...")
        from utils import setup_logging
        from yuque_downloader import YuqueDownloader
        
        setup_logging()
        downloader = YuqueDownloader()
        downloader.run()
        
    except KeyboardInterrupt:
        print("\n\n👋 用户中断，程序退出")
    except Exception as e:
        print(f"\n❌ 程序错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
