#!/bin/bash

# 语雀文档下载器终端版本启动脚本
# YuqueDownloader Terminal Version Launcher

# 获取脚本所在目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
cd "$SCRIPT_DIR"

echo "🚀 语雀文档下载器 - 终端版本"
echo "=============================="
echo ""
echo "✨ 特性："
echo "  📱 纯终端操作，无需图形界面"
echo "  📦 单文件可执行，便于部署"
echo "  🔄 包含所有依赖，开箱即用"
echo "  🎯 支持拦截器和API两种模式"
echo ""
echo "📁 当前目录结构："
echo "  yuque-downloader    - 主程序 (32MB)"
echo "  drivers/            - ChromeDriver"
echo "  downloads/          - 下载文件夹"
echo "  logs/               - 日志文件夹"
echo ""
echo "📂 工作目录: $SCRIPT_DIR"
echo ""
echo "🎯 启动程序..."
echo ""

# 检查ChromeDriver
if [ ! -f "drivers/chromedriver" ]; then
    echo "❌ 错误: ChromeDriver不存在"
    echo "   请确保 drivers/chromedriver 文件存在且可执行"
    exit 1
fi

# 检查权限
if [ ! -x "yuque-downloader" ]; then
    echo "🔧 修复可执行权限..."
    chmod +x yuque-downloader
fi

if [ ! -x "drivers/chromedriver" ]; then
    echo "🔧 修复ChromeDriver权限..."
    chmod +x drivers/chromedriver
fi

# 启动程序
./yuque-downloader 