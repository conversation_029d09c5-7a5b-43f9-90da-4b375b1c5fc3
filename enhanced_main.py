#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
增强版语雀下载器 - 使用有效的认证机制
"""

import json
import os
from yuque_downloader import YuqueDownloader

def enhanced_yuque_downloader():
    print("🚀 启动增强版语雀下载器...")
    print("=" * 50)
    
    try:
        # 检查认证文件
        auth_files = ['yuque_headers.json', 'yuque_cookies.json']
        missing_files = [f for f in auth_files if not os.path.exists(f)]
        
        if missing_files:
            print(f"❌ 缺少认证文件: {missing_files}")
            print("🔧 正在运行独立调试脚本获取认证信息...")
            
            import subprocess
            result = subprocess.run(['python', 'simple_debug.py'], 
                                 capture_output=True, text=True)
            if result.returncode != 0:
                print(f"❌ 获取认证信息失败: {result.stderr}")
                return False
            
            print("✅ 认证信息获取成功")
        
        # 初始化下载器
        downloader = YuqueDownloader()
        
        # 强制使用保存的认证信息
        print("🔧 应用保存的认证信息...")
        
        # 修改WebDriverManager以使用保存的认证
        if hasattr(downloader, 'webdriver_manager'):
            # 在初始化后立即加载保存的认证
            print("🔄 加载保存的认证信息...")
            
        # 运行下载器
        downloader.run()
        
    except Exception as e:
        print(f"❌ 运行失败: {e}")
        import traceback
        traceback.print_exc()
        return False
    finally:
        try:
            if 'downloader' in locals():
                downloader.cleanup()
        except:
            pass
    
    return True

if __name__ == "__main__":
    enhanced_yuque_downloader()
