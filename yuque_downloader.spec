# -*- mode: python ; coding: utf-8 -*-

block_cipher = None

# 收集所有Python文件和数据文件
a = Analysis(
    ['main.py'],
    pathex=['.'],
    binaries=[],
    datas=[
        ('requirements.txt', '.'),
        ('README.md', '.'),
        ('LICENSE', '.'),
        ('INSTALL.md', '.'),
        ('config.py', '.'),
        ('*.py', '.'),
    ],
    hiddenimports=[
        'selenium',
        'seleniumwire',
        'requests',
        'tqdm',
        'beautifulsoup4',
        'urllib3',
        'certifi',
        'charset_normalizer',
        'idna',
        'websocket-client',
        'trio',
        'trio_websocket',
        'attrs',
        'sortedcontainers',
        'outcome',
        'sniffio',
        'h11',
        'wsproto',
        'typing_extensions'
    ],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[
        'tkinter',
        'matplotlib',
        'numpy',
        'pandas',
        'PIL',
        'cv2',
        'scipy'
    ],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    [],
    exclude_binaries=True,
    name='YuqueDownloader',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    console=True,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
)

coll = COLLECT(
    exe,
    a.binaries,
    a.zipfiles,
    a.datas,
    strip=False,
    upx=True,
    upx_exclude=[],
    name='YuqueDownloader'
)

# macOS应用包
app = BUNDLE(
    coll,
    name='YuqueDownloader.app',
    icon=None,  # 可以添加.icns图标文件路径
    bundle_identifier='com.yuquedownloader.app',
    version='2.0.0',
    info_plist={
        'CFBundleName': 'YuqueDownloader',
        'CFBundleDisplayName': '语雀下载工具',
        'CFBundleVersion': '2.0.0',
        'CFBundleShortVersionString': '2.0.0',
        'CFBundleIdentifier': 'com.yuquedownloader.app',
        'CFBundleInfoDictionaryVersion': '6.0',
        'CFBundlePackageType': 'APPL',
        'LSMinimumSystemVersion': '10.14.0',
        'NSRequiresAquaSystemAppearance': False,
        'NSHighResolutionCapable': True,
        'CFBundleDocumentTypes': [],
        'LSApplicationCategoryType': 'public.app-category.productivity',
        'NSHumanReadableCopyright': 'Copyright © 2024 YuqueDownloader Team. All rights reserved.',
    }
) 