"""
用户交互界面模块
负责命令行用户界面，支持知识库和文档的多种选择方式
"""

import re
import logging
from typing import List, Dict, Optional, Union
from tqdm import tqdm
from config import CONSOLE_OUTPUT_CONFIG

logger = logging.getLogger(__name__)


class UserInterface:
    """用户交互界面"""
    
    def __init__(self):
        self.selected_books = []
        self.selected_docs = []
    
    def _deduplicate_books_by_id(self, books: List[Dict]) -> List[Dict]:
        """根据ID去重书籍列表"""
        try:
            seen_ids = set()
            unique_books = []
            
            for book in books:
                book_id = book.get('id')
                if book_id is not None and book_id not in seen_ids:
                    seen_ids.add(book_id)
                    unique_books.append(book)
                elif book_id in seen_ids:
                    logger.debug(f"跳过重复书籍: ID={book_id}, 名称={book.get('name', '未知')}")
            
            return unique_books
            
        except Exception as e:
            logger.error(f"去重操作失败: {e}")
            return books  # 如果去重失败，返回原列表
    
    def show_main_menu(self) -> str:
        """显示主菜单并获取用户选择"""
        print("\n" + "="*60)
        print("🚀 语雀文档下载器 - 主菜单")
        print("="*60)
        print("1. 拦截书籍列表 (推荐) - 自动拦截API获取书籍信息")
        print("2. 传统模式 - 使用浏览器手动获取书籍列表")
        print("3. 查看已保存的书籍列表")
        print("4. 退出程序")
        print("="*60)
        
        while True:
            choice = input("\n请选择操作模式 (1-4): ").strip()
            
            if choice in ['1', '2', '3', '4']:
                return choice
            else:
                print("❌ 无效选择，请输入 1、2、3 或 4")
    
    def show_interceptor_menu(self) -> str:
        """显示拦截器菜单"""
        print("\n" + "="*60)
        print("📡 书籍拦截器 - 操作选项")
        print("="*60)
        print("1. 拦截并获取最新书籍列表")
        print("2. 使用已保存的书籍列表")
        print("3. 返回主菜单")
        print("="*60)
        
        while True:
            choice = input("\n请选择操作 (1-3): ").strip()
            
            if choice in ['1', '2', '3']:
                return choice
            else:
                print("❌ 无效选择，请输入 1、2 或 3")
    
    def show_interceptor_status(self, status: str, details: str = ""):
        """显示拦截器状态"""
        status_icons = {
            "initializing": "🔧",
            "navigating": "🌐", 
            "waiting": "⏳",
            "intercepting": "📡",
            "processing": "⚙️",
            "success": "✅",
            "error": "❌",
            "warning": "⚠️"
        }
        
        icon = status_icons.get(status, "ℹ️")
        print(f"{icon} {details}")
    
    def confirm_interceptor_action(self, action: str) -> bool:
        """确认拦截器操作"""
        print(f"\n📡 准备执行: {action}")
        print("💡 提示: 拦截过程需要打开浏览器并自动访问语雀页面")
        print("💡 提示: 如果需要登录，请在浏览器中完成登录操作")
        
        while True:
            choice = input("\n是否继续? (y/n): ").strip().lower()
            if choice in ['y', 'yes', '是']:
                return True
            elif choice in ['n', 'no', '否']:
                return False
            else:
                print("❌ 请输入 y 或 n")

    def display_books(self, books: List[Dict]) -> None:
        """展示知识库列表"""
        print("\n" + "="*60)
        print("📚 可用知识库列表:")
        print("="*60)
        
        for i, book in enumerate(books, 1):
            book_name = book.get('name', '未知知识库')
            book_id = book.get('id', 'unknown')
            description = book.get('description', '')
            
            print(f"{i:2d}. {book_name}")
            if description:
                print(f"     📝 {description[:50]}{'...' if len(description) > 50 else ''}")
            print(f"     🆔 ID: {book_id}")
            print()
    
    def select_books(self, books: List[Dict], refresh_callback=None) -> List[Dict]:
        """处理用户选择知识库"""
        while True:
            print("\n📋 选择知识库:")
            print("  • 输入 'a' 选择全部知识库")
            print("  • 输入数字序号（用逗号分隔）选择特定知识库 (如: 1, 2, 3)")
            print("  • 输入关键字进行模糊匹配")
            print("  • 输入 'q' 退出程序")

            user_input = input("\n请输入您的选择: ").strip()

            if user_input.lower() == 'q':
                return []

            # 支持 'a' 快捷选择全部
            if user_input.lower() == 'a':
                self.selected_books = books
                print(f"✅ 已选择全部 {len(books)} 个知识库")
                return books

            # 保持向后兼容性，支持 '全部'
            if user_input == '全部':
                self.selected_books = books
                print(f"✅ 已选择全部 {len(books)} 个知识库")
                return books

            # 尝试解析为序号
            if self._is_number_sequence(user_input):
                selected = self._select_by_numbers(books, user_input)
                if selected:
                    self.selected_books = selected
                    return selected

            # 关键字匹配
            matched = self._match_books_by_keyword(books, user_input)
            if matched:
                print(f"\n🔍 找到 {len(matched)} 个匹配的知识库:")
                for i, book in enumerate(matched, 1):
                    print(f"  {i}. {book.get('name', '未知知识库')}")

                confirm = input("\n是否选择这些知识库? (y/n): ").strip().lower()
                if confirm in ['y', 'yes', '是']:
                    self.selected_books = matched
                    return matched
            else:
                print("❌ 未找到匹配的知识库，请重新输入")

    def select_books_and_mode(self, books: List[Dict], refresh_callback=None) -> tuple:
        """合并知识库选择和下载模式选择"""
        print("\n" + "="*60)
        print("📚 知识库选择和下载配置")
        print("="*60)

        # 先进行去重处理，但不显示列表
        unique_books = self._deduplicate_books_by_id(books)
        if len(unique_books) != len(books):
            removed_count = len(books) - len(unique_books)
            print(f"🔄 已去除 {removed_count} 个重复的知识库")
            books = unique_books
        
        print(f"📊 当前共有 {len(books)} 个可用知识库")

        print("\n📋 选择操作:")
        print("1. 选择知识库并下载所有文档 (推荐)")
        print("2. 选择知识库并为每个知识库单独选择文档")
        print("3. 下载所有知识库的所有文档 (快速模式)")
        if refresh_callback:
            print("4. 重新获取知识库列表")
        print()

        while True:
            choice_prompt = "请选择操作模式 (1/2/3"
            if refresh_callback:
                choice_prompt += "/4"
            choice_prompt += "): "

            choice = input(choice_prompt).strip()

            if choice == "1":
                print("✅ 已选择：选择知识库 + 下载所有文档")
                # 现在才显示知识库列表
                self.display_books(books)
                selected_books = self.select_books(books, refresh_callback)
                return selected_books, "all"
            elif choice == "2":
                print("✅ 已选择：选择知识库 + 单独选择文档")
                # 现在才显示知识库列表
                self.display_books(books)
                selected_books = self.select_books(books, refresh_callback)
                return selected_books, "select"
            elif choice == "3":
                print("✅ 已选择：快速模式 - 下载所有知识库的所有文档")
                return books, "all"
            elif choice == "4" and refresh_callback:
                print("🔄 正在重新获取知识库列表...")
                try:
                    new_books = refresh_callback()
                    if new_books:
                        books = new_books
                        print(f"✅ 成功获取 {len(books)} 个知识库")
                        self.display_books(books)
                        continue
                    else:
                        print("❌ 获取知识库列表失败")
                        continue
                except Exception as e:
                    print(f"❌ 获取知识库列表时出错: {e}")
                    continue
            else:
                error_msg = "❌ 无效选择，请输入 1、2 或 3"
                if refresh_callback:
                    error_msg = "❌ 无效选择，请输入 1、2、3 或 4"
                print(error_msg)

    def select_download_mode(self) -> str:
        """选择下载模式（保留兼容性）"""
        print("\n" + "="*60)
        print("📥 选择下载模式:")
        print("="*60)
        print("1. 下载所有文档 (推荐)")
        print("2. 为每个知识库单独选择文档")
        print()

        while True:
            choice = input("请选择下载模式 (1/2): ").strip()

            if choice == "1":
                print("✅ 已选择：下载所有文档")
                return "all"
            elif choice == "2":
                print("✅ 已选择：为每个知识库单独选择文档")
                return "select"
            else:
                print("❌ 无效选择，请输入 1 或 2")

    def display_docs(self, docs: List[Dict], book_name: str) -> None:
        """展示文档列表"""
        print(f"\n" + "="*60)
        print(f"📄 知识库 '{book_name}' 的文档列表:")
        print("="*60)
        
        valid_docs = []
        board_docs = []
        
        for i, doc in enumerate(docs, 1):
            doc_title = doc.get('title', '未知文档')
            doc_type = doc.get('type', 'unknown')
            doc_id = doc.get('id', 'unknown')
            
            if doc_type == 'Board':
                board_docs.append(doc_title)
                continue
            
            valid_docs.append(doc)
            print(f"{len(valid_docs):2d}. {doc_title}")
            print(f"     📋 类型: {doc_type}")
            print(f"     🆔 ID: {doc_id}")
            print()
        
        if board_docs:
            print("⚠️  以下Board类型文档需要手动导出，已自动跳过:")
            for board_doc in board_docs:
                print(f"     • {board_doc}")
            print()
        
        # 添加Sheet类型文档提示
        sheet_docs = [doc.get('title', '未知文档') for doc in docs if doc.get('type') == 'Sheet']
        if sheet_docs:
            print("📊 以下Sheet (表格) 类型文档也需要手动导出:")
            for sheet_doc in sheet_docs:
                print(f"     • {sheet_doc}")
            print("💡 提示: Sheet文档请在浏览器中手动导出为Excel或CSV格式")
            print()
        
        return valid_docs
    
    def select_docs(self, docs: List[Dict], book_name: str) -> List[Dict]:
        """处理用户选择文档"""
        # 过滤掉Board类型文档
        valid_docs = [doc for doc in docs if doc.get('type') != 'Board']
        
        if not valid_docs:
            print("❌ 该知识库没有可下载的文档")
            return []
        
        while True:
            print(f"\n📋 选择 '{book_name}' 中的文档:")
            print("  • 输入序号 (如: 1, 2, 3)")
            print("  • 输入 '全部' 选择所有文档")
            print("  • 输入关键字进行模糊匹配")
            print("  • 输入 's' 跳过此知识库")
            
            user_input = input("\n请输入您的选择: ").strip()
            
            if user_input.lower() == 's':
                return []
            
            if user_input == '全部':
                print(f"✅ 已选择全部 {len(valid_docs)} 个文档")
                return valid_docs
            
            # 尝试解析为序号
            if self._is_number_sequence(user_input):
                selected = self._select_by_numbers(valid_docs, user_input)
                if selected:
                    return selected
            
            # 关键字匹配
            matched = self._match_docs_by_keyword(valid_docs, user_input)
            if matched:
                print(f"\n🔍 找到 {len(matched)} 个匹配的文档:")
                for i, doc in enumerate(matched, 1):
                    print(f"  {i}. {doc.get('title', '未知文档')}")
                
                confirm = input("\n是否选择这些文档? (y/n): ").strip().lower()
                if confirm in ['y', 'yes', '是']:
                    return matched
            else:
                print("❌ 未找到匹配的文档，请重新输入")
    
    def _is_number_sequence(self, text: str) -> bool:
        """检查是否为数字序列"""
        return bool(re.match(r'^[\d,\s]+$', text))
    
    def _select_by_numbers(self, items: List[Dict], numbers_str: str) -> List[Dict]:
        """根据序号选择项目"""
        try:
            numbers = [int(x.strip()) for x in re.split(r'[,\s]+', numbers_str) if x.strip()]
            selected = []
            
            for num in numbers:
                if 1 <= num <= len(items):
                    selected.append(items[num - 1])
                else:
                    print(f"❌ 序号 {num} 超出范围 (1-{len(items)})")
                    return []
            
            if selected:
                print(f"✅ 已选择 {len(selected)} 个项目")
                return selected
            
        except ValueError:
            print("❌ 序号格式错误，请输入有效的数字")
        
        return []
    
    def _match_books_by_keyword(self, books: List[Dict], keyword: str) -> List[Dict]:
        """根据关键字匹配知识库"""
        keyword = keyword.lower()
        matched = []
        
        for book in books:
            book_name = book.get('name', '').lower()
            description = book.get('description', '').lower()
            
            if keyword in book_name or keyword in description:
                matched.append(book)
        
        return matched
    
    def _match_docs_by_keyword(self, docs: List[Dict], keyword: str) -> List[Dict]:
        """根据关键字匹配文档"""
        keyword = keyword.lower()
        matched = []
        
        for doc in docs:
            doc_title = doc.get('title', '').lower()
            
            if keyword in doc_title:
                matched.append(doc)
        
        return matched
    
    def show_progress(self, current: int, total: int, item_name: str):
        """显示进度信息"""
        print(f"\n📊 进度: {current}/{total} - 正在处理: {item_name}")
    
    def show_download_progress(self, desc: str) -> tqdm:
        """创建下载进度条"""
        return tqdm(desc=desc, unit='B', unit_scale=True, unit_divisor=1024)
    
    def print_summary(self, total_books: int, total_docs: int, success_count: int, failed_count: int, skipped_count: int = 0):
        """打印下载摘要"""
        print("\n" + "="*60)
        print("📊 下载完成摘要")
        print("="*60)
        print(f"📚 处理知识库数量: {total_books}")
        print(f"📄 总文档数量: {total_docs}")
        print(f"✅ 成功下载: {success_count}")
        if skipped_count > 0:
            print(f"⏭️ 跳过重复文件: {skipped_count}")
        print(f"❌ 下载失败: {failed_count}")
        print("="*60)

        # 添加Sheet文档手动导出提示
        print("\n💡 重要提示:")
        print("📊 Sheet (表格) 类型的文档需要手动导出")
        print("📋 手动导出步骤:")
        print("   1. 在浏览器中打开相关的Sheet文档")
        print("   2. 点击右上角的 '...' 菜单")
        print("   3. 选择 '导出' 选项")
        print("   4. 选择合适的导出格式 (Excel, CSV等)")
        print("   5. 下载到本地保存")

        print("\n" + "="*60)

        if failed_count > 0:
            print("⚠️  部分文档下载失败，请检查日志文件获取详细信息")
        elif skipped_count > 0:
            print("🎉 下载完成！部分文件因已存在而跳过")
        else:
            print("🎉 所有文档下载成功！")
    
    def confirm_download_with_summary(self, selected_books: List[Dict], download_mode: str) -> bool:
        """显示下载摘要并确认"""
        print("\n" + "="*60)
        print("📋 下载确认摘要")
        print("="*60)

        print(f"📚 选中的知识库 ({len(selected_books)} 个):")
        for i, book in enumerate(selected_books, 1):
            name = book.get('name', '未知知识库')
            items_count = book.get('items_count', '未知')
            print(f"   {i}. {name} ({items_count} 个文档)")

        mode_text = "下载所有文档" if download_mode == "all" else "为每个知识库单独选择文档"
        print(f"\n📥 下载模式: {mode_text}")

        if download_mode == "all":
            total_docs = sum(book.get('items_count', 0) for book in selected_books if isinstance(book.get('items_count'), int))
            if total_docs > 0:
                print(f"📊 预计下载文档数: {total_docs} 个")

        print("\n" + "="*60)
        response = input("确认开始下载吗？ (y/n): ").strip().lower()
        return response in ['y', 'yes', '是']

    def confirm_action(self, message: str) -> bool:
        """确认操作"""
        response = input(f"\n{message} (y/n): ").strip().lower()
        return response in ['y', 'yes', '是']

    def show_api_status(self, api_result):
        """显示API请求状态"""
        if not CONSOLE_OUTPUT_CONFIG.get("show_api_status", True):
            return

        method = api_result.method.upper() if api_result.method else "UNKNOWN"
        endpoint = api_result.get_endpoint()
        status_code = api_result.status_code or "N/A"

        if api_result.success:
            status_icon = "✅"
            status_text = f"{status_code} OK"
        else:
            status_icon = "❌"
            status_text = f"{status_code} ERROR"

        print(f"[API] {method} {endpoint} -> {status_text} {status_icon}")

        # 如果配置允许显示详细错误且有错误信息
        if (not api_result.success and
            CONSOLE_OUTPUT_CONFIG.get("show_detailed_errors", False) and
            api_result.error_msg):
            print(f"      错误详情: {api_result.error_msg}")

    def show_simple_progress(self, current: int, total: int, item_name: str):
        """显示简化的进度信息"""
        if not CONSOLE_OUTPUT_CONFIG.get("show_progress", True):
            return
        print(f"📊 {current}/{total} - {item_name}")
