#!/usr/bin/env python3
"""
语雀文档下载器主程序入口
使用原生selenium-wire进行请求拦截，支持会话记忆
"""

import sys
import os
from pathlib import Path

# 添加当前目录到Python路径
sys.path.insert(0, str(Path(__file__).parent))


def check_webdriver_manager():
    """检查WebDriver管理器是否正确"""
    try:
        # 检查是否是正确版本
        with open("yuque_webdriver_manager.py", 'r', encoding='utf-8') as f:
            content = f.read()

        if 'seleniumwire' in content and 'WebDriverManager' in content:
            print("✅ WebDriver管理器版本正确")
            print("🔍 请求拦截功能已启用")
            print("💾 会话记忆功能已启用")
            print("🖼️ 图片显示功能已恢复")
            return True
        else:
            print("❌ WebDriver管理器版本不正确")
            return False

    except Exception as e:
        print(f"❌ 检查WebDriver管理器失败: {e}")
        return False


def check_dependencies():
    """检查依赖是否完整"""
    missing_deps = []
    
    try:
        import seleniumwire
        print("✅ selenium-wire 可用")
    except ImportError:
        missing_deps.append("selenium-wire")
    
    try:
        import selenium
        print("✅ selenium 可用")
    except ImportError:
        missing_deps.append("selenium")
    
    try:
        import requests
        print("✅ requests 可用")
    except ImportError:
        missing_deps.append("requests")
    
    try:
        import tqdm
        print("✅ tqdm 可用")
    except ImportError:
        missing_deps.append("tqdm")
    
    if missing_deps:
        print(f"❌ 缺少依赖包: {', '.join(missing_deps)}")
        print("请运行以下命令安装:")
        print("pip install 'urllib3<2.0' 'blinker<1.5' selenium-wire==5.1.0")
        return False
    
    return True


def show_session_info():
    """显示会话信息"""
    session_files = [
        "yuque_cookies.json",
        "yuque_headers.json", 
        "yuque_session.json"
    ]
    
    print("\n📁 会话文件状态:")
    for file in session_files:
        if os.path.exists(file):
            stat = os.stat(file)
            import time
            mtime = time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(stat.st_mtime))
            print(f"  ✅ {file} (修改时间: {mtime})")
        else:
            print(f"  ❌ {file} (不存在)")


def main():
    """主函数"""
    print("🚀 语雀文档下载器启动")
    print("=" * 60)
    print("✨ 核心功能:")
    print("  📡 拦截器模式：自动拦截API获取书籍信息")
    print("  🔍 传统模式：使用selenium-wire进行请求拦截")
    print("  💾 支持会话记忆（cookies + headers + 请求历史）")
    print("  🖼️ 完整图片显示")
    print("  ⚡ 原生Selenium，稳定可靠")
    print("  📁 动态路径管理，便于部署")
    print("=" * 60)

    # 设置环境和路径
    print("\n🔧 初始化环境...")
    try:
        from path_manager import setup_environment, path_manager
        dynamic_config = setup_environment()
        path_manager.print_environment_info()
    except Exception as e:
        print(f"❌ 环境初始化失败: {e}")
        return

    # 检查依赖
    print("\n🔧 检查依赖...")
    if not check_dependencies():
        return

    # 显示会话信息
    show_session_info()

    # 检查WebDriver管理器版本
    print("\n🔄 检查WebDriver管理器...")
    if not check_webdriver_manager():
        print("❌ WebDriver管理器版本不正确")
        return
    
    try:
        # 导入并运行主程序
        from utils import setup_logging
        from yuque_downloader import YuqueDownloader
        from exceptions import YuqueException
        
        # 设置日志
        setup_logging()
        
        print("\n🎯 启动语雀文档下载器...")
        print("💡 提示: 支持多种获取书籍列表的方式")
        print("💡 提示: 拦截器模式可以自动获取最新书籍信息")
        print("💡 提示: 传统模式会自动拦截API请求并提取认证信息")
        print("💡 提示: 退出后会保存会话，下次启动时自动恢复")
        
        # 创建并运行下载器
        downloader = YuqueDownloader()
        downloader.run()
        
    except KeyboardInterrupt:
        print("\n\n👋 用户中断，程序退出")
    except Exception as e:
        print(f"\n❌ 程序错误: {e}")
        print("请查看日志文件获取详细信息")
    finally:
        # 显示最终会话信息
        print("\n📊 最终会话状态:")
        show_session_info()
        
        # 程序结束提示
        print("\n👋 程序已结束")


if __name__ == "__main__":
    main()
