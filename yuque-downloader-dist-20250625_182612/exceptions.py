"""
自定义异常模块
定义语雀下载器专用的异常类
"""


class YuqueException(Exception):
    """语雀下载器基础异常"""
    
    def __init__(self, message: str, error_code: str = None):
        self.message = message
        self.error_code = error_code
        super().__init__(self.message)


class WebDriverException(YuqueException):
    """WebDriver相关异常"""
    pass


class LoginException(YuqueException):
    """登录相关异常"""
    pass


class APIException(YuqueException):
    """API请求相关异常"""
    
    def __init__(self, message: str, status_code: int = None, response_data: dict = None):
        super().__init__(message)
        self.status_code = status_code
        self.response_data = response_data


class DownloadException(YuqueException):
    """下载相关异常"""
    
    def __init__(self, message: str, url: str = None, file_path: str = None):
        super().__init__(message)
        self.url = url
        self.file_path = file_path


class FileException(YuqueException):
    """文件操作相关异常"""
    
    def __init__(self, message: str, file_path: str = None):
        super().__init__(message)
        self.file_path = file_path


class ValidationException(YuqueException):
    """数据验证相关异常"""
    pass


class ConfigException(YuqueException):
    """配置相关异常"""
    pass


class NetworkException(YuqueException):
    """网络相关异常"""
    
    def __init__(self, message: str, url: str = None, timeout: int = None):
        super().__init__(message)
        self.url = url
        self.timeout = timeout


class ExportException(YuqueException):
    """文档导出相关异常"""
    
    def __init__(self, message: str, doc_id: str = None, export_type: str = None):
        super().__init__(message)
        self.doc_id = doc_id
        self.export_type = export_type


class UserInterruptException(YuqueException):
    """用户中断异常"""
    pass
