"""
工具函数模块
包含字符串匹配、重试装饰器、日志工具等通用功能
"""

import re
import time
import logging
import functools
from typing import Callable, Any
from config import RETRY_CONFIG, LOG_CONFIG, CONSOLE_OUTPUT_CONFIG


def setup_logging():
    """设置日志配置"""
    # 获取控制台日志级别
    console_level = CONSOLE_OUTPUT_CONFIG.get("console_log_level", "WARNING")

    # 创建根日志器
    root_logger = logging.getLogger()
    root_logger.setLevel(logging.DEBUG)

    # 清除现有处理器
    root_logger.handlers.clear()

    # 文件处理器 - 记录所有详细信息
    file_handler = logging.FileHandler(LOG_CONFIG["file"], encoding='utf-8')
    file_handler.setLevel(getattr(logging, LOG_CONFIG["level"]))
    file_formatter = logging.Formatter(LOG_CONFIG["format"])
    file_handler.setFormatter(file_formatter)
    root_logger.addHandler(file_handler)

    # 控制台处理器 - 只显示关键信息
    console_handler = logging.StreamHandler()
    console_handler.setLevel(getattr(logging, console_level))
    console_formatter = logging.Formatter("%(levelname)s: %(message)s")
    console_handler.setFormatter(console_formatter)
    root_logger.addHandler(console_handler)


def retry_on_failure(max_attempts: int = None, delay: float = None, backoff: float = None):
    """重试装饰器"""
    if max_attempts is None:
        max_attempts = RETRY_CONFIG["max_attempts"]
    if delay is None:
        delay = RETRY_CONFIG["delay"]
    if backoff is None:
        backoff = RETRY_CONFIG["backoff"]
    
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        def wrapper(*args, **kwargs) -> Any:
            last_exception = None
            current_delay = delay
            
            for attempt in range(max_attempts):
                try:
                    return func(*args, **kwargs)
                except Exception as e:
                    last_exception = e
                    if attempt < max_attempts - 1:
                        logging.warning(f"函数 {func.__name__} 第 {attempt + 1} 次尝试失败: {e}")
                        logging.info(f"等待 {current_delay} 秒后重试...")
                        time.sleep(current_delay)
                        current_delay *= backoff
                    else:
                        logging.error(f"函数 {func.__name__} 所有 {max_attempts} 次尝试都失败了")
            
            raise last_exception
        
        return wrapper
    return decorator


def sanitize_filename(filename: str, max_length: int = 200) -> str:
    """清理文件名，移除非法字符"""
    # 移除或替换非法字符
    illegal_chars = r'[<>:"/\\|?*]'
    filename = re.sub(illegal_chars, '_', filename)
    
    # 移除控制字符
    filename = re.sub(r'[\x00-\x1f\x7f-\x9f]', '', filename)
    
    # 移除首尾空格和点
    filename = filename.strip(' .')
    
    # 限制长度
    if len(filename) > max_length:
        name, ext = split_filename(filename)
        max_name_length = max_length - len(ext) - 1
        filename = name[:max_name_length] + '.' + ext if ext else name[:max_length]
    
    # 确保文件名不为空
    if not filename:
        filename = "untitled"
    
    return filename


def split_filename(filename: str) -> tuple:
    """分离文件名和扩展名"""
    if '.' in filename:
        parts = filename.rsplit('.', 1)
        return parts[0], parts[1]
    return filename, ''


def format_file_size(size_bytes: int) -> str:
    """格式化文件大小"""
    if size_bytes == 0:
        return "0B"
    
    size_names = ["B", "KB", "MB", "GB", "TB"]
    i = 0
    while size_bytes >= 1024 and i < len(size_names) - 1:
        size_bytes /= 1024.0
        i += 1
    
    return f"{size_bytes:.1f}{size_names[i]}"


def is_valid_url(url: str) -> bool:
    """检查URL是否有效"""
    url_pattern = re.compile(
        r'^https?://'  # http:// or https://
        r'(?:(?:[A-Z0-9](?:[A-Z0-9-]{0,61}[A-Z0-9])?\.)+[A-Z]{2,6}\.?|'  # domain...
        r'localhost|'  # localhost...
        r'\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3})'  # ...or ip
        r'(?::\d+)?'  # optional port
        r'(?:/?|[/?]\S+)$', re.IGNORECASE)
    
    return url_pattern.match(url) is not None


def normalize_text(text: str) -> str:
    """标准化文本，用于比较"""
    return re.sub(r'\s+', ' ', text.strip().lower())


def fuzzy_match(text: str, pattern: str, threshold: float = 0.6) -> bool:
    """模糊匹配文本"""
    text = normalize_text(text)
    pattern = normalize_text(pattern)
    
    if pattern in text:
        return True
    
    # 简单的相似度计算
    if len(pattern) == 0:
        return False
    
    matches = sum(1 for char in pattern if char in text)
    similarity = matches / len(pattern)
    
    return similarity >= threshold


def safe_get(dictionary: dict, key: str, default: Any = None) -> Any:
    """安全获取字典值"""
    try:
        return dictionary.get(key, default)
    except (AttributeError, TypeError):
        return default


def truncate_text(text: str, max_length: int = 50, suffix: str = "...") -> str:
    """截断文本"""
    if len(text) <= max_length:
        return text
    return text[:max_length - len(suffix)] + suffix


def validate_book_data(book: dict) -> bool:
    """验证知识库数据完整性"""
    if not isinstance(book, dict):
        return False
    required_fields = ['id', 'name']
    return all(field in book and book[field] for field in required_fields)


def normalize_books_data(data) -> list:
    """标准化知识库数据格式，处理各种可能的API响应格式"""
    if data is None:
        return []

    # 处理语雀 book_stacks API 的实际响应格式
    if isinstance(data, list):
        books = []
        for item in data:
            if isinstance(item, dict):
                # 如果是 book_stack 格式（有books字段），提取其中的 books
                if 'books' in item and isinstance(item['books'], list):
                    books.extend(item['books'])
                # 如果直接是知识库对象（必须有type字段且为Book）
                elif item.get('type') == 'Book' and validate_book_data(item):
                    books.append(item)
                # 如果没有type字段但有slug字段（语雀知识库的特征），且通过验证
                elif 'slug' in item and 'type' not in item and validate_book_data(item):
                    books.append(item)
        return books

    # 如果是字典，尝试提取books数据
    if isinstance(data, dict):
        # 语雀实际格式：{"data": [{"books": [...]}]}
        if 'data' in data and isinstance(data['data'], list):
            books = []
            for item in data['data']:
                if isinstance(item, dict) and 'books' in item and isinstance(item['books'], list):
                    books.extend(item['books'])
            return books

        # 标准格式：{"data": {"books": [...]}}
        if 'data' in data and isinstance(data['data'], dict) and 'books' in data['data']:
            return data['data']['books']

        # 简化格式：{"books": [...]}
        if 'books' in data and isinstance(data['books'], list):
            return data['books']

        # 如果字典本身就是单个知识库数据
        if validate_book_data(data):
            return [data]

    # 其他情况返回空列表
    return []


def validate_doc_data(doc: dict) -> bool:
    """验证文档数据完整性"""
    required_fields = ['id', 'title', 'type']
    return all(field in doc and doc[field] for field in required_fields)


def create_progress_callback(ui, desc: str):
    """创建进度回调函数"""
    def callback(current: int, total: int):
        if hasattr(ui, 'show_progress'):
            ui.show_progress(current, total, desc)
    return callback


def log_function_call(func: Callable) -> Callable:
    """记录函数调用的装饰器"""
    @functools.wraps(func)
    def wrapper(*args, **kwargs):
        logger = logging.getLogger(func.__module__)
        logger.debug(f"调用函数 {func.__name__}")
        
        try:
            result = func(*args, **kwargs)
            logger.debug(f"函数 {func.__name__} 执行成功")
            return result
        except Exception as e:
            logger.error(f"函数 {func.__name__} 执行失败: {e}")
            raise
    
    return wrapper


def ensure_directory_exists(directory_path: str) -> bool:
    """确保目录存在"""
    import os
    try:
        os.makedirs(directory_path, exist_ok=True)
        return True
    except Exception as e:
        logging.error(f"创建目录失败 {directory_path}: {e}")
        return False


def get_timestamp() -> str:
    """获取当前时间戳字符串"""
    import datetime
    return datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
