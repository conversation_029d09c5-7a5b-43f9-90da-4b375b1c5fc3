"""
语雀书籍列表拦截器
使用selenium-wire截取user_books API响应，提取书籍ID和名称
"""

import json
import time
import logging
from typing import List, Dict, Optional
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from yuque_webdriver_manager import WebDriverManager

logger = logging.getLogger(__name__)


class YuqueBooksInterceptor:
    """语雀书籍列表拦截器 - 基于selenium-wire"""
    
    def __init__(self):
        self.webdriver_manager = None
        self.intercepted_books = []
        self.target_api_pattern = "user_books"
        
    def initialize(self) -> bool:
        """初始化WebDriver管理器"""
        try:
            logger.info("初始化语雀书籍拦截器...")
            self.webdriver_manager = WebDriverManager()
            self.webdriver_manager.initialize_driver()
            logger.info("WebDriver初始化成功")
            return True
        except Exception as e:
            logger.error(f"WebDriver初始化失败: {e}")
            return False
    
    def intercept_books_api(self, target_url: str = "https://fjzx.yuque.com/dashboard/books", 
                           wait_timeout: int = 30) -> List[Dict]:
        """
        截取书籍列表API响应
        
        Args:
            target_url: 目标页面URL
            wait_timeout: 等待超时时间（秒）
            
        Returns:
            List[Dict]: 提取的书籍信息列表
        """
        try:
            if not self.webdriver_manager or not self.webdriver_manager.driver:
                logger.error("WebDriver未初始化")
                return []
            
            driver = self.webdriver_manager.driver
            
            # 清空之前的请求记录
            driver.requests.clear()
            self.intercepted_books.clear()
            
            logger.info(f"导航到目标页面: {target_url}")
            driver.get(target_url)
            
            # 等待页面加载完成
            time.sleep(3)
            
            # 检查是否需要登录
            current_url = driver.current_url
            if 'login' in current_url.lower():
                print("\n" + "="*60)
                print("🔐 检测到需要登录语雀账号")
                print("="*60)
                print("请在浏览器中完成登录")
                print("登录后请手动导航到知识库列表页面:")
                print(f"  {target_url}")
                print("等待页面加载完成后，程序将自动提取书籍信息")
                print("="*60)
                
                # 等待用户登录并导航到正确页面
                self._wait_for_books_page(target_url, wait_timeout + 120)  # 延长等待时间
            
            # 开始监听和分析网络请求
            books = self._analyze_network_requests()
            
            if not books:
                driver.refresh()
                time.sleep(5)
                books = self._analyze_network_requests()
            
            if books:
                logger.info(f"成功提取到 {len(books)} 个书籍信息")
                self.intercepted_books = books
                self._print_books_summary(books)
                return books
            else:
                logger.warning("未找到书籍API响应数据")
                return []
                
        except Exception as e:
            logger.error(f"拦截API过程中出错: {e}")
            return []
    
    def _wait_for_books_page(self, target_url: str, timeout: int):
        """等待用户导航到书籍页面"""
        driver = self.webdriver_manager.driver
        start_time = time.time()
        
        while time.time() - start_time < timeout:
            current_url = driver.current_url
            
            # 检查是否已经在书籍页面
            if ('dashboard/books' in current_url or 
                'yuque.com' in current_url and 'login' not in current_url.lower()):
                logger.info("检测到用户已登录并访问正确页面")
                return True
            
            # 每10秒提示一次
            elapsed = time.time() - start_time
            if int(elapsed) % 10 == 0 and elapsed > 0:
                print(f"   ⏳ 等待登录和页面导航... ({int(elapsed)}/{timeout}秒)")
            
            time.sleep(2)
        
        logger.warning("等待超时")
        return False
    
    def _analyze_network_requests(self) -> List[Dict]:
        """分析网络请求，提取书籍API响应"""
        try:
            if not self.webdriver_manager or not self.webdriver_manager.driver:
                return []
                
            driver = self.webdriver_manager.driver
            books = []
            
            # 分析所有请求
            for request in driver.requests:
                if request.response and self.target_api_pattern in request.url:
                    logger.info(f"找到目标API请求: {request.url}")
                    
                    try:
                        # 获取响应内容
                        response_body = request.response.body
                        if response_body:
                            # 处理gzip压缩的响应
                            try:
                                import gzip
                                # 尝试先解压缩
                                if response_body.startswith(b'\x1f\x8b'):  # gzip魔术数字
                                    response_text = gzip.decompress(response_body).decode('utf-8')
                                else:
                                    response_text = response_body.decode('utf-8')
                            except:
                                # 如果解压失败，尝试直接解码
                                response_text = response_body.decode('utf-8', errors='ignore')
                            
                            # 尝试解析JSON
                            json_data = json.loads(response_text)
                            
                            # 提取书籍信息
                            extracted_books = self._extract_books_from_response(json_data)
                            if extracted_books:
                                books.extend(extracted_books)
                    
                    except json.JSONDecodeError as e:
                        logger.error(f"JSON解析失败: {e}")
                    except Exception as e:
                        logger.error(f"处理API响应时出错: {e}")
            
            # 根据ID去重
            if books:
                books = self._deduplicate_books_by_id(books)
                logger.info(f"去重后剩余 {len(books)} 个书籍")
            
            return books
            
        except Exception as e:
            logger.error(f"分析网络请求时出错: {e}")
            return []
    
    def _deduplicate_books_by_id(self, books: List[Dict]) -> List[Dict]:
        """根据ID去重书籍列表"""
        try:
            seen_ids = set()
            unique_books = []
            
            for book in books:
                book_id = book.get('id')
                if book_id is not None and book_id not in seen_ids:
                    seen_ids.add(book_id)
                    unique_books.append(book)
                elif book_id in seen_ids:
                    logger.debug(f"跳过重复书籍: ID={book_id}, 名称={book.get('name', '未知')}")
            
            removed_count = len(books) - len(unique_books)
            if removed_count > 0:
                logger.info(f"已去除 {removed_count} 个重复书籍")
                # 不向用户显示去重信息，只记录到日志
            
            return unique_books
            
        except Exception as e:
            logger.error(f"去重操作失败: {e}")
            return books  # 如果去重失败，返回原列表
    
    def _extract_books_from_response(self, json_data) -> List[Dict]:
        """从API响应中提取书籍信息"""
        try:
            books_info = []
            
            # 处理不同的响应格式
            if isinstance(json_data, list):
                # 直接是书籍列表
                data_list = json_data
            elif isinstance(json_data, dict):
                # 从字典中提取数据列表
                data_list = json_data.get('data', json_data.get('books', json_data.get('items', [])))
                
                # 如果data字段不是列表，可能是包装在其他结构中
                if not isinstance(data_list, list):
                    logger.debug(f"响应数据格式: {json_data.keys() if isinstance(json_data, dict) else type(json_data)}")
                    return []
            else:
                logger.warning(f"未知的API响应格式: {type(json_data)}")
                return []
            
            # 提取每个书籍的关键信息
            for item in data_list:
                if isinstance(item, dict) and 'id' in item and 'name' in item:
                    book_info = {
                        'id': item['id'],
                        'name': item['name'],
                        'description': item.get('description', ''),
                        'items_count': item.get('items_count', 0),
                        'slug': item.get('slug', ''),
                        'user_id': item.get('user_id'),
                        'organization_id': item.get('organization_id'),
                        'created_at': item.get('created_at'),
                        'updated_at': item.get('updated_at'),
                        'public': item.get('public', 0),
                        'type': item.get('type', 'Book')
                    }
                    books_info.append(book_info)
                    logger.debug(f"提取书籍: ID={book_info['id']}, 名称={book_info['name']}")
            
            return books_info
            
        except Exception as e:
            logger.error(f"提取书籍信息失败: {e}")
            return []
    
    def _print_books_summary(self, books: List[Dict]):
        """打印书籍信息摘要"""
        if not books:
            print("❌ 未获取到任何书籍信息")
            return
        
        # 只记录到日志，不向用户显示具体数量和详情
        logger.info(f"成功截取到 {len(books)} 个书籍")
        for i, book in enumerate(books, 1):
            logger.debug(f"{i:2d}. ID: {book['id']:<12} | 名称: {book['name']}")
            if book.get('description'):
                logger.debug(f"     描述: {book['description'][:50]}{'...' if len(book.get('description', '')) > 50 else ''}")
            logger.debug(f"     文档数: {book.get('items_count', 0)} | 类型: {book.get('type', 'Book')}")
    
    def save_books_to_file(self, filename: str = "yuque_books.json") -> bool:
        """保存书籍信息到文件"""
        try:
            if not self.intercepted_books:
                logger.warning("没有书籍信息可保存")
                return False
            
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(self.intercepted_books, f, ensure_ascii=False, indent=2)
            
            logger.info(f"书籍信息已保存到: {filename}")
            # 不向用户显示保存信息，只记录到日志
            return True
            
        except Exception as e:
            logger.error(f"保存文件失败: {e}")
            return False
    
    def get_books_list(self) -> List[Dict]:
        """获取截取到的书籍列表"""
        return self.intercepted_books.copy()
    
    def get_books_id_name_pairs(self) -> List[Dict]:
        """获取书籍ID和名称对"""
        return [{'id': book['id'], 'name': book['name']} for book in self.intercepted_books]
    
    def close(self):
        """关闭资源"""
        if self.webdriver_manager:
            self.webdriver_manager.close()


def main():
    """主函数演示"""
    interceptor = YuqueBooksInterceptor()
    
    try:
        print("🚀 启动语雀书籍拦截器")
        
        # 初始化
        if not interceptor.initialize():
            print("❌ 初始化失败")
            return
        
        # 拦截书籍API
        books = interceptor.intercept_books_api()
        
        if books:
            # 保存完整数据
            interceptor.save_books_to_file("yuque_books.json")
            
            # 保存ID名称对
            id_name_pairs = interceptor.get_books_id_name_pairs()
            with open("yuque_books_id_name.json", 'w', encoding='utf-8') as f:
                json.dump(id_name_pairs, f, ensure_ascii=False, indent=2)
            
            print(f"✅ 成功获取 {len(books)} 个书籍信息")
            print("📁 数据已保存到 yuque_books.json 和 yuque_books_id_name.json")
        else:
            print("❌ 未获取到书籍信息")
    
    except Exception as e:
        print(f"❌ 程序执行出错: {e}")
    
    finally:
        # 清理资源
        interceptor.close()
        print("🔄 资源清理完成")


if __name__ == "__main__":
    # 设置日志
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    main() 