"""
路径管理器 - 处理ChromeDriver和Chrome用户数据目录的动态路径管理
"""

import os
import sys
import platform
from pathlib import Path
import logging

logger = logging.getLogger(__name__)

class PathManager:
    """路径管理器，处理程序运行时的路径配置"""
    
    def __init__(self):
        self.base_dir = self._get_base_directory()
        self.system = platform.system()
        
    def _get_base_directory(self) -> Path:
        """获取程序基础目录"""
        if getattr(sys, 'frozen', False):
            # 如果是打包后的可执行文件
            base_dir = Path(sys.executable).parent
        else:
            # 如果是Python脚本
            base_dir = Path(__file__).parent
        
        logger.info(f"程序基础目录: {base_dir}")
        return base_dir
    
    def get_chromedriver_path(self) -> str:
        """获取ChromeDriver路径，自动检测可用的ChromeDriver"""
        possible_paths = [
            # 相对于程序目录的路径
            self.base_dir / "drivers" / "chromedriver",
            self.base_dir / "chromedriver",
            self.base_dir / "drivers" / "chromedriver.exe",
            self.base_dir / "chromedriver.exe",
            
            # 系统PATH中的ChromeDriver
            "chromedriver"
        ]
        
        # 根据操作系统添加扩展名
        if self.system == "Windows":
            for i, path in enumerate(possible_paths[:4]):  # 只处理本地路径
                if not str(path).endswith('.exe'):
                    possible_paths[i] = Path(str(path) + '.exe')
        
        for path in possible_paths:
            try:
                if isinstance(path, Path):
                    if path.exists() and path.is_file():
                        # 检查文件是否可执行
                        if os.access(path, os.X_OK) or self.system == "Windows":
                            logger.info(f"找到ChromeDriver: {path}")
                            return str(path.resolve())
                else:
                    # 检查系统PATH中的chromedriver
                    import shutil
                    system_path = shutil.which(path)
                    if system_path:
                        logger.info(f"找到系统ChromeDriver: {system_path}")
                        return system_path
            except Exception as e:
                logger.debug(f"检查ChromeDriver路径失败 {path}: {e}")
                continue
        
        # 如果没有找到，返回默认路径并提示用户
        default_path = self.base_dir / "drivers" / "chromedriver"
        if self.system == "Windows":
            default_path = Path(str(default_path) + '.exe')
        
        logger.warning(f"未找到ChromeDriver，将使用默认路径: {default_path}")
        logger.warning("请确保ChromeDriver已下载并放置在正确位置")
        
        return str(default_path)
    
    def get_chrome_user_data_dir(self) -> str:
        """获取Chrome用户数据目录，自动创建如果不存在"""
        user_data_dir = self.base_dir / "chrome_user_data"
        
        try:
            # 确保目录存在
            user_data_dir.mkdir(parents=True, exist_ok=True)
            
            # 检查目录是否可写
            test_file = user_data_dir / ".test_write"
            try:
                test_file.write_text("test")
                test_file.unlink()
                logger.info(f"Chrome用户数据目录: {user_data_dir}")
                return str(user_data_dir.resolve())
            except Exception as e:
                logger.error(f"Chrome用户数据目录不可写: {e}")
                
        except Exception as e:
            logger.error(f"创建Chrome用户数据目录失败: {e}")
        
        # 如果创建失败，尝试使用临时目录
        import tempfile
        temp_dir = Path(tempfile.gettempdir()) / "yuque_downloader_chrome"
        temp_dir.mkdir(parents=True, exist_ok=True)
        
        logger.warning(f"使用临时Chrome用户数据目录: {temp_dir}")
        return str(temp_dir)
    
    def setup_directories(self):
        """设置必要的目录结构"""
        directories = [
            self.base_dir / "downloads",
            self.base_dir / "drivers",
            self.base_dir / "chrome_user_data",
            self.base_dir / "logs"
        ]
        
        for directory in directories:
            try:
                directory.mkdir(parents=True, exist_ok=True)
                logger.debug(f"确保目录存在: {directory}")
            except Exception as e:
                logger.error(f"创建目录失败 {directory}: {e}")
    
    def check_chromedriver_executable(self, chromedriver_path: str) -> bool:
        """检查ChromeDriver是否可执行"""
        try:
            path = Path(chromedriver_path)
            if not path.exists():
                return False
            
            if not path.is_file():
                return False
            
            # 在Windows上，检查是否有.exe扩展名
            if self.system == "Windows" and not path.suffix.lower() == '.exe':
                return False
            
            # 检查是否可执行
            if not os.access(path, os.X_OK) and self.system != "Windows":
                return False
            
            return True
            
        except Exception as e:
            logger.error(f"检查ChromeDriver可执行性失败: {e}")
            return False
    
    def get_download_directory(self) -> str:
        """获取下载目录"""
        download_dir = self.base_dir / "downloads"
        download_dir.mkdir(parents=True, exist_ok=True)
        return str(download_dir)
    
    def get_log_directory(self) -> str:
        """获取日志目录"""
        log_dir = self.base_dir / "logs"
        log_dir.mkdir(parents=True, exist_ok=True)
        return str(log_dir)
    
    def print_environment_info(self):
        """打印环境信息"""
        print("🔧 环境配置信息:")
        print(f"   操作系统: {self.system}")
        print(f"   程序目录: {self.base_dir}")
        print(f"   ChromeDriver: {self.get_chromedriver_path()}")
        print(f"   Chrome用户数据: {self.get_chrome_user_data_dir()}")
        print(f"   下载目录: {self.get_download_directory()}")
        
        # 检查ChromeDriver状态
        chromedriver_path = self.get_chromedriver_path()
        if self.check_chromedriver_executable(chromedriver_path):
            print(f"   ✅ ChromeDriver可用")
        else:
            print(f"   ❌ ChromeDriver不可用，请检查路径和权限")

# 全局路径管理器实例
path_manager = PathManager()

def get_dynamic_config():
    """获取动态配置，覆盖config.py中的静态配置"""
    return {
        "chromedriver_path": path_manager.get_chromedriver_path(),
        "chrome_user_data_dir": path_manager.get_chrome_user_data_dir(),
        "base_dir": path_manager.get_download_directory()
    }

def setup_environment():
    """设置运行环境"""
    path_manager.setup_directories()
    return get_dynamic_config()
